<!doctype html>
<html lang="en">
    <head>
        <meta charset="utf-8">
        <title>SVG stack preview | svg-sprite</title>
        <style>
            body {
                padding: 0;
                margin: 0;
                color: #666;
                background: #fafafa;
                font-family: Arial, Helvetica, sans-serif;
                font-size: 1em;
                line-height: 1.4;
            }

            header {
                display: block;
                padding: 3em 3em 2em;
                background-color: #fff;
            }

            header p {
                margin: 2em 0 0;
            }

            section {
                border-top: 1px solid #eee;
                padding: 2em 3em 0;
            }

            section ul {
                margin: 0;
                padding: 0;
            }

            section li {
                display: inline-block;
                background-color: #fff;
                position: relative;
                margin: 0 2em 2em 0;
                vertical-align: top;
                border: 1px solid #ccc;
                padding: 1em 1em 3em;
                cursor: default;
            }

            .icon-box {
                margin: 0;
                width: 144px;
                height: 144px;
                position: relative;
                background: #ccc url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12'%3E%3Cpath fill='%23fff' d='M6 0h6v6H6zM0 6h6v6H0z'/%3E%3C/svg%3E") top left repeat;
                border: 1px solid #ccc;
                display: table-cell;
                vertical-align: middle;
                text-align: center;
            }

            .icon {
                display: inline-block;
            }

            h1 {
                margin-top: 0;
            }

            h2 {
                margin: 0;
                padding: 0;
                font-size: 1em;
                font-weight: 400;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                position: absolute;
                left: 1em;
                right: 1em;
                bottom: 1em;
            }

            footer {
                display: block;
                margin: 0;
                padding: 0 3em 3em;
            }

            footer p {
                margin: 0;
                font-size: .7em;
            }

            footer a {
                color: #0f7595;
                margin-left: 0;
            }
        </style>

<!--
Sprite shape dimensions
====================================================================================================
You will need to set the sprite shape dimensions via CSS when you use them as stack SVGs, otherwise
they would become a huge 100% in size. You may use the following dimension classes for doing so.
They might well be outsourced to an external stylesheet of course.
-->

<style>
 .svg-heart-dims { width: 24px; height: 25px; }
 .svg-heart-line-dims { width: 24px; height: 24px; }
 .svg-id-card-line-dims { width: 24px; height: 24px; }
 .svg-search-dims { width: 24px; height: 24px; }
 .svg-search-line-dims { width: 24px; height: 24px; }
 .svg-user-dims { width: 24px; height: 24px; }
 .svg-user-star-line-dims { width: 24px; height: 24px; }
 .svg-website-dims { width: 512px; height: 512px; }
</style>

<!--
====================================================================================================
-->

    </head>
    <body>
        <header>
            <h1>SVG stack preview</h1>
            <p>This preview features an SVG stack. Please have a look at the HTML source for further details and be aware of the following constraints:</p>
            <ul>
                <li>Your browser has to <a href="https://caniuse.com/svg-fragment" target="_blank" rel="noopener noreferrer">support SVG fragment identifiers</a> for SVG stacks to work.</li>
                <li>Support for SVG fragment identifiers hasn't always been that decent. For older browsers you will have to use some prolyfill like <a href="https://github.com/preciousforever/SVG-Stacker/blob/master/fixsvgstack.jquery.js" target="_blank" rel="noopener noreferrer">fixsvgstack.jquery.js</a>.</li>
            </ul>
        </header>
        <section>

<!--
SVG stack
====================================================================================================
These SVG images make use of fragment identifiers (IDs) to reference certain portions of the
external sprite. By default, all shapes inside the sprite are hidden by CSS. The `:target` pseudo
selector is used to show the very shape that is referenced by the fragment identifier.
-->

            <ul>

             <li title="heart">
                    <div class="icon-box">
                        <img src="svg/sprite.stack.svg#heart" class="svg-heart-dims" alt="heart">
                    </div>
                    <h2>heart, </h2>
                </li>
             <li title="heart-line">
                    <div class="icon-box">
                        <img src="svg/sprite.stack.svg#heart-line" class="svg-heart-line-dims" alt="heart-line">
                    </div>
                    <h2>heart-line, </h2>
                </li>
             <li title="id-card-line">
                    <div class="icon-box">
                        <img src="svg/sprite.stack.svg#id-card-line" class="svg-id-card-line-dims" alt="id-card-line">
                    </div>
                    <h2>id-card-line, </h2>
                </li>
             <li title="search">
                    <div class="icon-box">
                        <img src="svg/sprite.stack.svg#search" class="svg-search-dims" alt="search">
                    </div>
                    <h2>search, </h2>
                </li>
             <li title="search-line">
                    <div class="icon-box">
                        <img src="svg/sprite.stack.svg#search-line" class="svg-search-line-dims" alt="search-line">
                    </div>
                    <h2>search-line, </h2>
                </li>
             <li title="user">
                    <div class="icon-box">
                        <img src="svg/sprite.stack.svg#user" class="svg-user-dims" alt="user">
                    </div>
                    <h2>user, </h2>
                </li>
             <li title="user-star-line">
                    <div class="icon-box">
                        <img src="svg/sprite.stack.svg#user-star-line" class="svg-user-star-line-dims" alt="user-star-line">
                    </div>
                    <h2>user-star-line, </h2>
                </li>
             <li title="website">
                    <div class="icon-box">
                        <img src="svg/sprite.stack.svg#website" class="svg-website-dims" alt="website">
                    </div>
                    <h2>website, </h2>
                </li>
         </ul>

<!--
====================================================================================================
-->

        </section>
        <footer>
            <p>Generated at Mon, 31 Mar 2025 04:19:24 GMT by <a href="https://github.com/svg-sprite/svg-sprite" target="_blank" rel="noopener noreferrer">svg-sprite</a>.</p>
        </footer>
    </body>
</html>
