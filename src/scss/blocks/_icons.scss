.icons-wrapper {
	padding: 30px 0;
	display: flex;
	column-gap: 30px;
}

.icon {
	fill: transparent;
	stroke: transparent;
	width: 62px;
	height: 62px;
}

.icon--contact-fill {
	fill: var(--icon-fill-white);
}
.icon--contact-stroke {
	stroke: var(--icon-fill-white);
}

.icon--heart-line {
	fill: rgb(241, 68, 131);
}

.icon--id-card-line {
	fill: rgb(51, 51, 51);
}
.icon--search-line {
	fill: rgb(28, 176, 80);
}

.icon--user-star {
	fill: rgb(26, 134, 235);
}

.icon--user {
	stroke: rgb(26, 134, 235);
	transition: all 0.2s ease-in;

	&:hover {
		stroke: rgb(17, 193, 90);
	}
}
