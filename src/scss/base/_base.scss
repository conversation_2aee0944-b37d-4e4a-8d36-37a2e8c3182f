@import url(https://fonts.googleapis.com/css?family=Manrope:200,300,regular,500,600,700,800);
:root {

}
html {
	font-size: 62.5%;
    height: 100%;
    width: 100%;
	scroll-behavior: smooth;

	// Responsive font scaling
	@media (max-width: 1500px) {
		font-size: 62.5%; // 1rem = 10px
	}
	@media (max-width: 992px) {
		font-size: 60%; // 1rem = 9.6px (slightly smaller for tablets)
	}
	@media (max-width: 767px) {
		font-size: 58%; // 1rem = 9.28px (smaller for mobile)
	}
	@media (max-width: 576px) {
		font-size: 56%; // 1rem = 8.96px (smallest for small mobile)
	}
}
body {
	background-color: var(--page-bg);
	color: var(--text-color);
	font-family: var(--font-main);
	text-wrap: balance;
}

img {
	display: block;
}

a {
	color: var(--link-color);
}

code {
	background-color: #e9f1f6;
	padding: 0.2rem;
	border-radius: 4px;
}

pre.code {
	overflow-x: auto;
	background-color: #e9f1f6;
	padding: 1rem;
	border-radius: 4px;
}
