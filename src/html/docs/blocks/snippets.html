<section>
	<div class="container">
		<h2 class="title-2">Полезные сниппеты</h2>
		<p>Сниппеты хранятся в папке <code>.vscode</code> в файле <code>webCademy.code-snippets</code></p>
		<h4 class="title-4">SCSS cниппеты для медиазапросов</h4>

		<p>Код вызова: <code>tablet</code></p>
		<pre class="code">@include tablet { ... }</pre>

		<p>Код вызова: <code>mobile</code></p>
		<pre class="code">@include mobile { ... }</pre>

		<p>Retina background-image через медиа запрос. Код вызова: <code>mediaBg</code></p>
<pre class="code">
@include mediaBg() {
	background-image: url('./../img/<EMAIL>');
}</pre>


		<p>Retina background-image через image-set. Код вызова: <code>imgSet</code></p>
		<pre class="code">background-image: image-set(
	url('./../img/bg.jpg') 1x,
	url('./../img/<EMAIL>') 2x);</pre>

		<h4 class="title-4">HTML cниппет для svg иконок из спрайта</h4>
		<p>Код вызова: <code>svgIcon</code></p>
<pre class="code">
&lt;svg class="icon icon--heart-line"&gt;
	&lt;use href="./img/svgsprite/sprite.symbol.svg#heart-line"&gt;&lt;/use&gt;
&lt;/svg&gt;
</pre>

	</div>
</section>